<?php
/**
 * Production Test Script
 * Tests database connection and file paths
 * DELETE THIS FILE AFTER TESTING
 */

echo "<h1>Production Environment Test</h1>";

// Test 0: Path Info
echo "<h2>0. Path Info</h2>";
echo "Testing from: " . __DIR__ . "<br>";
echo "Expected secure config: " . __DIR__ . "/../../secure_config/obq_comments.php<br>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test</h2>";

// First, let's see what config the database class actually loads
echo "<h3>Config Debug:</h3>";
try {
    // Clear any potential opcode cache
    if (function_exists('opcache_reset')) {
        opcache_reset();
        echo "✅ OpCache reset<br>";
    }

    // Load the config directly to see what it contains
    if (!defined('OBQ_CONFIG_ACCESS')) {
        define('OBQ_CONFIG_ACCESS', true);
    }
    $directConfig = include __DIR__ . '/comments/config.php';
    echo "Direct config load - Database host: " . $directConfig['database']['host'] . "<br>";
    echo "Direct config load - Database name: " . $directConfig['database']['dbname'] . "<br>";
    echo "Direct config load - Database username: " . $directConfig['database']['username'] . "<br>";

} catch (Exception $e) {
    echo "❌ Error loading config directly: " . $e->getMessage() . "<br>";
}

echo "<h3>Database Class Test:</h3>";
try {
    require_once __DIR__ . '/comments/database.php';
    $db = CommentDatabase::getInstance();

    // Get the config from the database instance
    $dbConfig = $db->getConfig('database');
    echo "Database class config - Host: " . $dbConfig['host'] . "<br>";
    echo "Database class config - Database: " . $dbConfig['dbname'] . "<br>";
    echo "Database class config - Username: " . $dbConfig['username'] . "<br>";

    echo "✅ Database connection successful<br>";

    // Test table existence
    $pdo = $db->getPDO();
    $stmt = $pdo->query("SHOW TABLES LIKE 'aachipsc_blog_%'");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "✅ Found " . count($tables) . " database tables<br>";

} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
    echo "This suggests there's a config loading issue in the CommentDatabase class.<br>";
}

// Test 2: File Path Resolution
echo "<h2>2. File Path Test</h2>";

$testPaths = [
    'Comments Display' => __DIR__ . '/comments/comments-display.php',
    'Visitor Counter' => __DIR__ . '/visitor-counter/VisitorCounter.php',
    'Visitor Display' => __DIR__ . '/visitor-counter/visitor-display.php',
    'Comments Config' => __DIR__ . '/comments/config.php'
];

// Test secure config - should be 2 levels up from obsidian-quartz
echo "<h3>Secure Config Test:</h3>";
$secureConfigPath = __DIR__ . '/../../secure_config/obq_comments.php';
echo "Looking for: $secureConfigPath<br>";

if (file_exists($secureConfigPath)) {
    echo "✅ Secure Config: Found!<br>";

    // Test if we can load it
    try {
        if (!defined('OBQ_CONFIG_ACCESS')) {
            define('OBQ_CONFIG_ACCESS', true);
        }
        $testConfig = require $secureConfigPath;
        if (isset($testConfig['database']['host'])) {
            echo "✅ Secure Config: Successfully loaded with database config<br>";
            echo "Database host: " . $testConfig['database']['host'] . "<br>";
            echo "Database name: " . $testConfig['database']['dbname'] . "<br>";
            echo "Database username: " . $testConfig['database']['username'] . "<br>";
            echo "Password length: " . strlen($testConfig['database']['password']) . " characters<br>";

            // Test basic connection without using the database class
            echo "<h4>Direct Connection Test:</h4>";
            try {
                $testPdo = new PDO(
                    "mysql:host={$testConfig['database']['host']};charset=utf8mb4",
                    $testConfig['database']['username'],
                    $testConfig['database']['password'],
                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                );
                echo "✅ Direct PDO connection successful<br>";

                // Test database selection
                $testPdo->exec("USE {$testConfig['database']['dbname']}");
                echo "✅ Database selection successful<br>";

            } catch (PDOException $e) {
                echo "❌ Direct connection failed: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "❌ Secure Config: Loaded but missing database configuration<br>";
        }
    } catch (Exception $e) {
        echo "❌ Secure Config: Error loading - " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Secure Config: Not found at expected location<br>";

    // Show what directories exist at each level
    $levels = ['..', '../..', '../../..'];
    foreach ($levels as $i => $level) {
        $fullPath = realpath(__DIR__ . '/' . $level);
        if ($fullPath && is_dir($fullPath . '/secure_config')) {
            echo "Found secure_config at level " . ($i + 1) . ": $fullPath/secure_config<br>";
        }
    }
}

foreach ($testPaths as $name => $path) {
    if (file_exists($path)) {
        echo "✅ $name: Found<br>";
    } else {
        echo "❌ $name: Missing ($path)<br>";
    }
}

// Test 3: Function Availability
echo "<h2>3. Function Test</h2>";
try {
    require_once __DIR__ . '/comments/comments-display.php';
    if (function_exists('renderCommentsSection')) {
        echo "✅ renderCommentsSection function available<br>";
    } else {
        echo "❌ renderCommentsSection function not found<br>";
    }
} catch (Exception $e) {
    echo "❌ Error loading comments display: " . $e->getMessage() . "<br>";
}

try {
    require_once __DIR__ . '/visitor-counter/visitor-display.php';
    if (function_exists('displayVisitorCounter')) {
        echo "✅ displayVisitorCounter function available<br>";
    } else {
        echo "❌ displayVisitorCounter function not found<br>";
    }
} catch (Exception $e) {
    echo "❌ Error loading visitor counter: " . $e->getMessage() . "<br>";
}

// Test 4: PHP Extensions
echo "<h2>4. PHP Extensions Test</h2>";
if (extension_loaded('pdo')) {
    echo "✅ PDO extension loaded<br>";
} else {
    echo "❌ PDO extension missing<br>";
}

if (extension_loaded('pdo_mysql')) {
    echo "✅ PDO MySQL extension loaded<br>";
} else {
    echo "❌ PDO MySQL extension missing<br>";
}

echo "<h2>Test Complete</h2>";
echo "<p><strong>Remember to delete this file after testing!</strong></p>";
?>