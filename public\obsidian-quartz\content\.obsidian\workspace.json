{"main": {"id": "0def73a3a0bfb5b4", "type": "split", "children": [{"id": "cda172b8d5a969c5", "type": "tabs", "children": [{"id": "a6c9b6c5a9324be5", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Wishlist Income Streams.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Wishlist Income Streams"}}]}], "direction": "vertical"}, "left": {"id": "e824ff613c91cbc2", "type": "split", "children": [{"id": "39bb0defc0c69b2f", "type": "tabs", "children": [{"id": "7f2b0bf6a71e00c4", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "af5e9a238e869c2d", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "13b85ff6ecc313b0", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "2620c9b21031015f", "type": "split", "children": [{"id": "4e1c99d91bc4bf79", "type": "tabs", "children": [{"id": "d88eb04bdd1662ab", "type": "leaf", "state": {"type": "backlink", "state": {"file": "Wishlist Income Streams.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for Wishlist Income Streams"}}, {"id": "f84234008a80f91c", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "Wishlist Income Streams.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from Wishlist Income Streams"}}, {"id": "a8c10da7a9b6e83b", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "0bd794996649d937", "type": "leaf", "state": {"type": "outline", "state": {"file": "Wishlist Income Streams.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of Wishlist Income Streams"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "a6c9b6c5a9324be5", "lastOpenFiles": ["Birthday Wishlist.md", "bday-wishlist.md", "the-wrong-turn", "Wishlist Income Streams.md", "Rainbow Gatherings and Radical Christianity.md", "Summer Story - Local Rainbow Gathering - The Wrong Turn.md", "i-am-not-smee.md", "access-tech", "safeguarding", "music/Music Section Index.md", "music", "inspiration/Happiness - Animated Film - Rats.md", "I accept cash and money gifts. You can vote third party with your dollar...md", "empty-space.md", "Birthday Campaign.md", "Birthday Brainstorm.md", "Analog Request System.md", "dreamers-blessing.md", "Untitled.md", "Personal Blog_ Choose Your Own Adventure Outline.md", "content_analysis.md", "choose_your_own_adventure_structure.md", "5 AM Prayers to the Algorithm.md"]}